import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/AIAnalyzerPage.css';

const AIAnalyzerPage = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [newProject, setNewProject] = useState({
    name: '',
    description: ''
  });




  // 翻译对象
  const translations = {
    en: {
      title: 'AI Site Analyzer',
      subtitle: 'Intelligent Industrial Site Analysis Platform',
      createProject: 'Create New Project',
      projectName: 'Project Name',
      projectDescription: 'Project Description',
      create: 'Create',
      cancel: 'Cancel',
      noProjects: 'No projects yet',
      noProjectsDesc: 'Create your first project to start analyzing industrial sites',
      createdAt: 'Created',
      openProject: 'Open Project',
      deleteProject: 'Delete',
      backToHome: 'Back to Home',
      selectLanguage: 'Select Language'
    },
    zh: {
      title: 'AI选址分析器',
      subtitle: '智能工业选址分析平台',
      createProject: '创建新项目',
      projectName: '项目名称',
      projectDescription: '项目描述',
      create: '创建',
      cancel: '取消',
      noProjects: '暂无项目',
      noProjectsDesc: '创建您的第一个项目开始分析工业选址',
      createdAt: '创建时间',
      openProject: '打开项目',
      deleteProject: '删除',
      backToHome: '返回首页',
      selectLanguage: '选择语言'
    }
  };

  const t = translations[language] || translations.en;



  // 加载项目列表
  useEffect(() => {
    const savedProjects = localStorage.getItem('aiAnalyzerProjects');
    if (savedProjects) {
      setProjects(JSON.parse(savedProjects));
    }
  }, []);





  // 保存项目到本地存储
  const saveProjects = useCallback((projectList) => {
    localStorage.setItem('aiAnalyzerProjects', JSON.stringify(projectList));
    setProjects(projectList);
  }, []);

  // 创建新项目
  const handleCreateProject = useCallback(() => {
    if (!newProject.name.trim()) return;

    const project = {
      id: Date.now().toString(),
      name: newProject.name.trim(),
      description: newProject.description.trim(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active'
    };

    const updatedProjects = [...projects, project];
    saveProjects(updatedProjects);
    setNewProject({ name: '', description: '' });
    setShowCreateModal(false);
  }, [newProject, projects, saveProjects]);

  // 删除项目
  const handleDeleteProject = useCallback((projectId) => {
    const updatedProjects = projects.filter(p => p.id !== projectId);
    saveProjects(updatedProjects);
  }, [projects, saveProjects]);

  // 打开项目
  const handleOpenProject = useCallback((projectId) => {
    navigate(`/ai-project/${projectId}`);
  }, [navigate]);

  // 语言切换
  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  }, []);

  return (
    <div className="ai-analyzer-page">
      {/* 背景效果 */}
      <div className="ai-background">
        <div className="grid-pattern"></div>
        <div className="floating-particles"></div>
      </div>

      {/* 顶部导航 */}
      <header className="ai-header">
        <div className="header-content">
          <button
            className="back-button"
            onClick={() => navigate('/')}
          >
            ← {t.backToHome}
          </button>

          <div className="header-center">
            <h1 className="page-title">{t.title}</h1>
            <p className="page-subtitle">{t.subtitle}</p>
          </div>

          {/* 语言切换按钮 */}
          <button
            onClick={() => {
              const newLang = language === 'en' ? 'zh' : 'en';
              handleLanguageChange(newLang);
            }}
            style={{
              background: 'rgba(77, 200, 255, 0.1)',
              border: '1px solid rgba(77, 200, 255, 0.3)',
              color: '#4dc8ff',
              padding: '10px 15px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(77, 200, 255, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(77, 200, 255, 0.1)';
            }}
          >
            {language === 'en' ? '🇺🇸 English' : '🇨🇳 中文'}
          </button>
        </div>
      </header>

      {/* 主内容 */}
      <main className="ai-main">
        <div className="ai-container">
          {/* 创建项目按钮 */}
          <div className="create-section">
            <button
              className="create-project-btn"
              onClick={() => setShowCreateModal(true)}
            >
              <span className="btn-icon">+</span>
              {t.createProject}
            </button>
          </div>

          {/* 项目列表 */}
          <div className="projects-section">
            {projects.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📊</div>
                <h3>{t.noProjects}</h3>
                <p>{t.noProjectsDesc}</p>
              </div>
            ) : (
              <div className="projects-grid">
                {projects.map(project => (
                  <div key={project.id} className="project-card">
                    <div className="project-header">
                      <h3 className="project-name">{project.name}</h3>
                      <div className="project-actions">
                        <button
                          className="action-btn open-btn"
                          onClick={() => handleOpenProject(project.id)}
                          title={t.openProject}
                        >
                          📂
                        </button>
                        <button
                          className="action-btn delete-btn"
                          onClick={() => handleDeleteProject(project.id)}
                          title={t.deleteProject}
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    {project.description && (
                      <p className="project-description">{project.description}</p>
                    )}
                    <div className="project-meta">
                      <span className="project-date">
                        {t.createdAt}: {new Date(project.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* 创建项目模态框 */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>{t.createProject}</h2>
            <div className="form-group">
              <label>{t.projectName}</label>
              <input
                type="text"
                value={newProject.name}
                onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t.projectName}
                maxLength={100}
              />
            </div>
            <div className="form-group">
              <label>{t.projectDescription}</label>
              <textarea
                value={newProject.description}
                onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t.projectDescription}
                rows={3}
                maxLength={500}
              />
            </div>
            <div className="modal-actions">
              <button
                className="btn-secondary"
                onClick={() => {
                  setShowCreateModal(false);
                  setNewProject({ name: '', description: '' });
                }}
              >
                {t.cancel}
              </button>
              <button
                className="btn-primary"
                onClick={handleCreateProject}
                disabled={!newProject.name.trim()}
              >
                {t.create}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAnalyzerPage;

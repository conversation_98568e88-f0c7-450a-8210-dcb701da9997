import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/AIProjectPage.css';

const AIProjectPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [inputText, setInputText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState(null);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showResultsOnly, setShowResultsOnly] = useState(false);
  const dropdownRef = useRef(null);

  const supportedLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ];

  const translations = {
    en: {
      backToProjects: 'Back to Projects',
      projectNotFound: 'Project not found',
      inputPlaceholder: 'Enter information about the industrial park you want to analyze...\n\nFor example:\n- Location details (city, state, country)\n- Transportation access (highways, ports, airports)\n- Available utilities and infrastructure\n- Target industries or business requirements\n- Size and zoning information',
      analyzeButton: 'Analyze with AI',
      analyzing: 'Analyzing...',
      analysisResults: 'Analysis Results',
      analysisSummaryTitle: 'Analysis Summary', // 新增
      extractedInfo: 'Extracted Information',
      cities: 'Cities',
      countries: 'Countries',
      highways: 'Highways',
      industries: 'Industries',
      states_provinces: 'States/Provinces',
      facility_type: 'Facility Type',
      specific_requirements: 'Specific Requirements',
      airports: 'Airports',
      ports: 'Ports',
      quantitative_info: 'Quantitative Info',
      other: 'Other Keywords',
      noResults: 'No analysis results yet',
      error: 'Error',
      tryAgain: 'Try Again',
      clearResults: 'Clear Results',
      selectLanguage: 'Select Language',
      focusResults: 'Focus on Results',
      showBoth: 'Show Both Panels',
      editResults: 'Click to edit',
      addItem: 'Add Item',
      deleteItem: 'Delete',
      saveChanges: 'Save Changes',
      exportResults: 'Export Results'
    },
    zh: {
      backToProjects: '返回项目列表',
      projectNotFound: '项目未找到',
      inputPlaceholder: '请输入您想要分析的工业园区信息...\n\n例如：\n- 位置详情（城市、州/省、国家）\n- 交通便利性（高速公路、港口、机场）\n- 可用设施和基础设施\n- 目标行业或业务需求\n- 规模和分区信息',
      analyzeButton: 'AI分析',
      analyzing: '分析中...',
      analysisResults: '分析结果',
      analysisSummaryTitle: '分析摘要', // 新增
      extractedInfo: '提取的信息',
      cities: '城市',
      countries: '国家',
      highways: '高速公路',
      industries: '行业',
      states_provinces: '州/省',
      facility_type: '设施类型',
      specific_requirements: '具体要求',
      airports: '机场',
      ports: '港口',
      quantitative_info: '量化信息',
      other: '其他关键词',
      noResults: '暂无分析结果',
      error: '错误',
      tryAgain: '重试',
      clearResults: '清除结果',
      selectLanguage: '选择语言',
      focusResults: '专注结果',
      showBoth: '显示双面板',
      editResults: '点击编辑',
      addItem: '添加项目',
      deleteItem: '删除',
      saveChanges: '保存更改',
      exportResults: '导出结果'
    }
  };

  const t = translations[language] || translations.en;

  const getCurrentLanguage = () => {
    return supportedLanguages.find(lang => lang.code === language) || supportedLanguages[0];
  };

  useEffect(() => {
    const savedProjects = localStorage.getItem('aiAnalyzerProjects');
    if (savedProjects) {
      const projects = JSON.parse(savedProjects);
      const currentProject = projects.find(p => p.id === id);
      setProject(currentProject);
    }
  }, [id]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLanguageDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setShowLanguageDropdown(false);
  }, []);

  const toggleLanguageDropdown = useCallback(() => {
    setShowLanguageDropdown(prev => !prev);
  }, []);

  const analyzeWithAI = useCallback(async () => {
    if (!inputText.trim()) return;

    setIsAnalyzing(true);
    setError(null);

    // =================================================================
    // V5.0
    // =================================================================
    const prompt = `
# ROLE AND GOAL
You are a Principal Analyst at a top-tier global industrial real estate and supply chain consultancy. Your client (the user) has provided a query for a potential site or facility. Your goal is to function as an expert system that performs a rigorous, multi-layered analysis of the user's input, delivering a structured, professional, and insightful data object.

# PRIMARY DIRECTIVES

1.  **Internal Analysis First (Chain-of-Thought)**: Before generating the JSON, mentally deconstruct the user's query.
    -   Identify the core geography (city, country, specific landmarks).
    -   List the explicit facility specifications (size, height, type).
    -   Recognize the stated business purpose (e.g., e-commerce, manufacturing).
    -   Pinpoint key infrastructure mentions (highways, ports).

2.  **Expert Inference and Enrichment**: This is your primary value. Go beyond literal extraction.
    -   **Connect Business Purpose to Needs**: If the user mentions "e-commerce," you *must* infer requirements like "last-mile delivery optimization," "high throughput sorting," and facility types like "fulfillment center." If they mention "biopharma," you *must* infer "GMP compliance," "clean rooms," "temperature control," etc.
    -   **Standardize and Formalize**: Normalize entities. "near Hamburg port" should be analyzed to identify \`city: "Hamburg"\` and \`ports: ["Port of Hamburg"]\`. "A7 autobahn" becomes \`highways: ["A7"]\`.

3.  **Strict Schema Adherence**: The output MUST be a single, valid JSON object and nothing else. No introductory text, no explanations, no apologies. If a field is not applicable or information is missing, use an empty array \`[]\` or \`null\` for string fields.

4.  **Language Consistency**: The language used for the values within the JSON MUST match the primary language of the user's input text.

5.  **Final Validation**: Before outputting, double-check your generated JSON against the schema provided below to ensure 100% compliance.

---
# JSON OUTPUT SCHEMA

{
  "analysis_summary": string, // A single, professional sentence summarizing the user's core requirement.
  "location": {
    "text_mention": string,      // The exact text the user used for location, e.g., "靠近上海港".
    "city": string | null,
    "state_province": string | null,
    "country": string | null,
    "proximity_notes": string[]  // Capture relative location descriptions, e.g., "Near Port of Shanghai", "20km from city center".
  },
  "facility": {
    "type": string[],              // e.g., ["Warehouse", "Distribution Center"]
    "explicit_requirements": string[], // Requirements explicitly stated by the user.
    "inferred_requirements": string[], // Requirements inferred by you, the expert.
    "quantitative_info": string[]  // e.g., ["Area: 5000 sqm", "Clear Height: 9m"]
  },
  "infrastructure": {
    "highways": string[],
    "airports": string[],
    "ports": string[]
  },
  "industry_tags": string[] // Keywords related to the user's industry and business.
}

---
# EXAMPLES

## Example 1: (Rich, Detailed Input in Chinese)
* **Input**: "我正在寻找一个靠近上海港的，大约5000平米，净高9米的现代化仓库，用于存放进口的高价值电子产品。需要有24小时安保和温湿度控制。"
* **Output**:
    {
      "analysis_summary": "Requirement for a modern, climate-controlled warehouse near the Port of Shanghai for high-value electronics storage.",
      "location": {
        "text_mention": "靠近上海港",
        "city": "上海",
        "state_province": "上海",
        "country": "中国",
        "proximity_notes": ["靠近上海港"]
      },
      "facility": {
        "type": ["现代化仓库", "高标仓", "配送中心"],
        "explicit_requirements": ["24小时安保", "温湿度控制", "用于存放进口的高价值电子产品"],
        "inferred_requirements": ["防静电措施", "高级别安防系统", "适用于高价值货物"],
        "quantitative_info": ["面积: 5000平米", "净高: 9米"]
      },
      "infrastructure": { "highways": [], "airports": [], "ports": ["上海港"] },
      "industry_tags": ["电子产品", "高科技", "供应链", "进口物流", "仓储"]
    }

## Example 2: (Standard Input with Enrichment in English)
* **Input**: "I need a logistics hub near Hamburg with good connection to the port and A7 autobahn, for e-commerce fulfillment."
* **Output**:
    {
      "analysis_summary": "Seeking an e-commerce logistics hub in the Hamburg area with direct access to the port and A7 highway.",
      "location": {
        "text_mention": "near Hamburg",
        "city": "Hamburg",
        "state_province": "Hamburg",
        "country": "Germany",
        "proximity_notes": ["Good connection to the port", "Good connection to A7 autobahn"]
      },
      "facility": {
        "type": ["logistics hub", "fulfillment center", "e-commerce warehouse"],
        "explicit_requirements": ["for e-commerce fulfillment"],
        "inferred_requirements": ["high throughput sorting", "last-mile delivery optimization", "scalable storage"],
        "quantitative_info": []
      },
      "infrastructure": { "highways": ["A7"], "airports": [], "ports": ["Port of Hamburg"] },
      "industry_tags": ["e-commerce", "logistics", "last-mile delivery", "3PL", "distribution"]
    }
---

# ANALYZE THE FOLLOWING USER TEXT:

**Input Text:** "${inputText}"

**JSON Output:**
`;

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${"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"}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          'model': 'tngtech/deepseek-r1t-chimera:free',
          'messages': [
            {
              'role': 'user',
              'content': prompt
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      try {
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const extractedData = JSON.parse(jsonMatch[0]);

          // **[MODIFIED]** Flatten the NEW nested JSON for UI display
          const displayData = {
            summary: extractedData.analysis_summary || '',
            cities: extractedData.location?.city ? [extractedData.location.city] : [],
            states_provinces: extractedData.location?.state_province ? [extractedData.location.state_province] : [],
            countries: extractedData.location?.country ? [extractedData.location.country] : [],
            highways: extractedData.infrastructure?.highways || [],
            ports: extractedData.infrastructure?.ports || [],
            airports: extractedData.infrastructure?.airports || [],
            facility_type: extractedData.facility?.type || [],
            specific_requirements: [
              ...(extractedData.facility?.explicit_requirements || []),
              ...(extractedData.facility?.inferred_requirements || [])
            ],
            quantitative_info: extractedData.facility?.quantitative_info || [],
            industries: extractedData.industry_tags || [],
            other: extractedData.location?.proximity_notes || [],
          };
          
          setAnalysisResult(displayData);

        } else {
          throw new Error('No valid JSON found in AI response');
        }
      } catch (parseError) {
        console.error('JSON parsing error:', parseError);
        setError('Failed to parse AI response. Raw output logged.');
        setAnalysisResult({ other: [aiResponse] });
      }

    } catch (err) {
      console.error('AI Analysis error:', err);
      setError(err.message);
    } finally {
      setIsAnalyzing(false);
    }
  }, [inputText]);

  const clearResults = useCallback(() => {
    setAnalysisResult(null);
    setError(null);
    setShowResultsOnly(false);
  }, []);
  
  const toggleViewMode = useCallback(() => {
    setShowResultsOnly(prev => !prev);
  }, []);

  const addItemToCategory = useCallback((category) => {
    const newItem = prompt(language === 'en' ? 'Enter new item:' : '输入新项目:');
    if (newItem && newItem.trim()) {
      setAnalysisResult(prev => ({
        ...prev,
        [category]: [...(prev[category] || []), newItem.trim()]
      }));
    }
  }, [language]);

  const deleteItem = useCallback((category, index) => {
    setAnalysisResult(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index)
    }));
  }, []);

  const exportResults = useCallback(() => {
    if (!analysisResult) return;
    const exportData = {
      projectName: project?.name || 'AI Analysis',
      timestamp: new Date().toISOString(),
      inputText,
      results: analysisResult
    };
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-analysis-${project?.name.replace(/\s/g, '_') || 'export'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analysisResult, project, inputText]);

  if (!project) {
    return (
      <div className="ai-project-page">
        <div className="ai-background"><div className="grid-pattern"></div></div>
        <div className="error-container">
          <h2>{t.projectNotFound}</h2>
          <button className="back-button" onClick={() => navigate('/ai-analyzer')}>{t.backToProjects}</button>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-project-page">
      <div className="ai-background">
        <div className="tech-circuit"></div>
      </div>
      <header className="ai-header">
        <div className="header-content">
          <button className="back-button modern-btn" onClick={() => navigate('/ai-analyzer')}>
            <span className="btn-icon">←</span><span className="btn-text">{t.backToProjects}</span>
          </button>
          <div className="project-info">
            <h1 className="project-title">{project.name}</h1>
            <div className="project-meta">
              <span className="project-status"><span className="status-dot active"></span>{language === 'en' ? 'Active Project' : '活跃项目'}</span>
              <span className="project-date">{new Date(project.createdAt || Date.now()).toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US')}</span>
            </div>
          </div>
          <div className="language-dropdown modern-dropdown" ref={dropdownRef}>
            <button className="language-dropdown-trigger modern-trigger" onClick={toggleLanguageDropdown}>
              <span className="current-language">
                <span className="language-flag">{getCurrentLanguage().flag}</span>
                <span className="language-name">{getCurrentLanguage().name}</span>
              </span>
              <span className={`dropdown-arrow ${showLanguageDropdown ? 'open' : ''}`}>
                <svg width="12" height="8" viewBox="0 0 12 8" fill="none"><path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </span>
            </button>
            {showLanguageDropdown && (
              <div className="language-dropdown-menu modern-menu">
                <div className="dropdown-header"><span className="header-icon">🌐</span>{t.selectLanguage}</div>
                {supportedLanguages.map((lang) => (
                  <button key={lang.code} className={`language-option modern-option ${language === lang.code ? 'active' : ''}`} onClick={() => handleLanguageChange(lang.code)}>
                    <span className="language-flag">{lang.flag}</span>
                    <span className="language-name">{lang.name}</span>
                    {language === lang.code && (<span className="check-mark"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></span>)}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </header>
      <main className="ai-main">
        <div className="ai-container">
          {analysisResult && (
            <div className="view-controls">
              <button className={`view-toggle-btn ${showResultsOnly ? 'active' : ''}`} onClick={toggleViewMode} title={showResultsOnly ? t.showBoth : t.focusResults}>
                <span className="btn-icon">
                  {showResultsOnly ? (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="7" height="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><rect x="14" y="3" width="7" height="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none"><rect x="14" y="3" width="7" height="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M10 3H3v18h7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </span>
                <span className="btn-text">{showResultsOnly ? t.showBoth : t.focusResults}</span>
              </button>
            </div>
          )}
          <div className={`workspace modern-workspace ${showResultsOnly ? 'results-only' : ''}`}>
            <div className={`input-section modern-section ${showResultsOnly ? 'hidden' : ''}`}>
              <div className="input-card modern-card">
                <div className="card-header">
                  <div className="header-content">
                    <div className="header-icon"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></div>
                    <div className="header-text">
                      <h2>{language === 'en' ? 'Input Information' : '输入信息'}</h2>
                      <p className="header-subtitle">{language === 'en' ? 'Describe your industrial park requirements in detail' : '详细描述您的工业园区需求'}</p>
                    </div>
                  </div>
                  <div className="input-stats"><span className="char-count">{inputText.length} {language === 'en' ? 'characters' : '字符'}</span></div>
                </div>
                <div className="input-container"><textarea className="input-textarea modern-textarea" value={inputText} onChange={(e) => setInputText(e.target.value)} placeholder={t.inputPlaceholder} rows={12}/></div>
                <div className="suggestion-chips">
                  {['Location', 'Transportation', 'Utilities', 'Industry'].map((chip, index) => (
                    <button key={index} className="suggestion-chip" onClick={() => {
                      const suggestions = { 0: 'Located in ', 1: 'Near highway ', 2: 'With utilities: ', 3: 'Suitable for industry: '};
                      setInputText(prev => prev + (language === 'zh' ? {0: '位于', 1: '靠近高速', 2: '拥有设施：', 3: '适用于行业：'}[index] : suggestions[index]));
                    }}>{language === 'en' ? chip : {0: '位置', 1: '交通', 2: '设施', 3: '行业'}[index]}</button>
                  ))}
                </div>
                <div className="input-actions modern-actions">
                  <button className="analyze-btn modern-primary-btn" onClick={analyzeWithAI} disabled={!inputText.trim() || isAnalyzing}>
                    {isAnalyzing ? (<><span className="loading-spinner modern-spinner"></span><span className="btn-text">{t.analyzing}</span></>) : (<><span className="btn-icon"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M2 17l10 5 10-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M2 12l10 5 10-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></span><span className="btn-text">{t.analyzeButton}</span></>)}
                  </button>
                  {analysisResult && (<button className="clear-btn modern-secondary-btn" onClick={clearResults}><span className="btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></span><span className="btn-text">{t.clearResults}</span></button>)}
                </div>
              </div>
            </div>
            <div className="results-section modern-section" style={{ marginTop: 0 }}>
              <div className="results-card modern-card">
                <div className="card-header">
                  <div className="header-content">
                    <div className="header-icon"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M3 3v18h18V3H3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M9 9h6v6H9z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M9 3v6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M15 3v6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M9 15v6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M15 15v6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M3 9h6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M3 15h6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M15 9h6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M15 15h6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></div>
                    <div className="header-text">
                      <h2>{t.analysisResults}</h2>
                      <p className="header-subtitle">{language === 'en' ? 'AI-powered analysis of your requirements' : 'AI智能分析您的需求'}</p>
                    </div>
                  </div>
                  {analysisResult && (<div className="results-stats"><span className="results-count">{Object.values(analysisResult).flat().length} {language === 'en' ? 'items found' : '项发现'}</span></div>)}
                </div>
                {error && (
                  <div className="error-message modern-error">
                    <div className="error-icon"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/><line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/><line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/></svg></div>
                    <div className="error-content">
                      <h4>{t.error}</h4><p>{error}</p>
                      <button className="retry-btn modern-retry-btn" onClick={analyzeWithAI}><span className="btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><polyline points="23,4 23,10 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></span>{t.tryAgain}</button>
                    </div>
                  </div>
                )}
                {!analysisResult && !error && (
                  <div className="no-results modern-empty">
                    <div className="empty-illustration"><svg width="120" height="120" viewBox="0 0 120 120" fill="none"><circle cx="60" cy="60" r="50" stroke="currentColor" strokeWidth="2" strokeDasharray="8 8" opacity="0.3"/><circle cx="60" cy="60" r="30" stroke="currentColor" strokeWidth="2" opacity="0.5"/><circle cx="60" cy="60" r="10" fill="currentColor" opacity="0.7"/><path d="M40 40L80 80M80 40L40 80" stroke="currentColor" strokeWidth="2" strokeLinecap="round" opacity="0.4"/></svg></div>
                    <h3>{t.noResults}</h3>
                    <p>{language === 'en' ? 'Enter your requirements above and click analyze to get started' : '在上方输入您的需求并点击分析开始'}</p>
                  </div>
                )}
                {analysisResult && (
                  <div className="analysis-results modern-results">
                    {/* [NEW] Analysis Summary Section */}
                    {analysisResult.summary && (
                      <div className="analysis-summary-section">
                        <h4>{t.analysisSummaryTitle}</h4>
                        <p>{analysisResult.summary}</p>
                      </div>
                    )}
                    <div className="results-header">
                      <h3>{t.extractedInfo}</h3>
                      <div className="results-actions">
                        <button className="export-btn modern-action-btn" onClick={exportResults} title={t.exportResults}><svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></button>
                        <div className="edit-hint"><span className="hint-icon">✏️</span><span className="hint-text">{t.editResults}</span></div>
                      </div>
                    </div>
                    <div className="results-list modern-list">
                      {Object.entries(analysisResult).map(([category, items]) => (
                        (items && items.length > 0 && category !== 'summary') && ( // Exclude summary from this list
                        <div key={category} className="result-category simple-category">
                          <div className="category-header enhanced-header">
                            <div className="header-left"><h4>{t[category] || category}</h4><span className="item-count">{items?.length || 0}</span></div>
                            <button className="add-item-btn" onClick={() => addItemToCategory(category)} title={t.addItem}><svg width="14" height="14" viewBox="0 0 24 24" fill="none"><line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></button>
                          </div>
                          <div className="result-items simple-items">
                            {items.map((item, index) => (
                              <div key={index} className="result-tag-container">
                                <span className="result-tag enhanced-tag" contentEditable suppressContentEditableWarning={true} onBlur={(e) => {
                                  const newValue = e.target.textContent;
                                  if (newValue !== item && newValue.trim()) {
                                    const newItems = [...items]; newItems[index] = newValue.trim();
                                    setAnalysisResult(prev => ({...prev, [category]: newItems}));
                                  } else if (!newValue.trim()) { e.target.textContent = item; }
                                }} onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); e.target.blur(); } }}>{item}</span>
                                <button className="delete-item-btn" onClick={() => deleteItem(category, index)} title={t.deleteItem}><svg width="12" height="12" viewBox="0 0 24 24" fill="none"><line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg></button>
                              </div>
                            ))}
                          </div>
                        </div>
                        )
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AIProjectPage;
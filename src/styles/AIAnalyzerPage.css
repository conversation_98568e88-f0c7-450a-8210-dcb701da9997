/* AI Analyzer Page Styles */
.ai-analyzer-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
  width: 100%;
  display: block;
}

/* 确保语言下拉菜单显示 */
.ai-analyzer-page .language-dropdown,
.ai-analyzer-page .language-dropdown-trigger {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 背景效果 */
.ai-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(77, 200, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(77, 200, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(77, 200, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(77, 200, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(77, 200, 255, 0.05) 0%, transparent 50%);
  animation: particleFloat 15s ease-in-out infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes particleFloat {
  0%, 100% { opacity: 0.3; transform: translateY(0px); }
  50% { opacity: 0.6; transform: translateY(-20px); }
}

/* 顶部导航 */
.ai-header {
  position: relative;
  z-index: 10;
  padding: 20px 0;
  border-bottom: 1px solid rgba(77, 200, 255, 0.2);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  position: relative;
  width: 100%;
}

.back-button {
  background: rgba(77, 200, 255, 0.1);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  flex-shrink: 0;
}

.back-button:hover {
  background: rgba(77, 200, 255, 0.2);
  transform: translateX(-2px);
}

/* 头部中心区域 */
.header-center {
  text-align: center;
  flex: 1;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 5px 0;
  text-shadow: 0 0 30px rgba(77, 200, 255, 0.3);
}

.page-subtitle {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}



/* 语言下拉菜单 */
.language-dropdown {
  position: relative;
  flex-shrink: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.language-dropdown-trigger {
  display: flex !important;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  backdrop-filter: blur(10px);
  min-width: 120px;
  z-index: 100;
  visibility: visible !important;
  opacity: 1 !important;
}

.language-dropdown-trigger:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(77, 200, 255, 0.4);
}

.current-language {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.dropdown-arrow {
  font-size: 10px;
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.6);
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: rgba(10, 15, 28, 0.95);
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  min-width: 200px;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  padding: 12px 16px;
  background: rgba(77, 200, 255, 0.1);
  color: #4dc8ff;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(77, 200, 255, 0.2);
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-align: left;
}

.language-option:hover {
  background: rgba(77, 200, 255, 0.1);
  color: #fff;
}

.language-option.active {
  background: rgba(77, 200, 255, 0.2);
  color: #4dc8ff;
}

.language-flag {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.language-name {
  flex: 1;
  font-weight: 500;
}

.check-mark {
  color: #4dc8ff;
  font-weight: bold;
  font-size: 12px;
}

/* 主内容 */
.ai-main {
  position: relative;
  z-index: 5;
  padding: 40px 0;
}

.ai-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}



/* 创建项目按钮 */
.create-section {
  text-align: center;
  margin-bottom: 50px;
}

.create-project-btn {
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  border: none;
  color: #0a0f1c;
  padding: 15px 30px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 8px 25px rgba(77, 200, 255, 0.3);
}

.create-project-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(77, 200, 255, 0.4);
}

.btn-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 项目列表 */
.projects-section {
  margin-top: 40px;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 16px;
  padding: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.project-card:hover {
  transform: translateY(-5px);
  border-color: rgba(77, 200, 255, 0.4);
  box-shadow: 0 15px 35px rgba(77, 200, 255, 0.2);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.project-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  flex: 1;
}

.project-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  width: 35px;
  height: 35px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.open-btn:hover {
  background: rgba(77, 200, 255, 0.3);
  border-color: #4dc8ff;
}

.delete-btn:hover {
  background: rgba(255, 71, 87, 0.3);
  border-color: #ff4757;
}

.project-description {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 15px;
  line-height: 1.5;
}

.project-meta {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
}

.project-date {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 16px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-content h2 {
  color: #4dc8ff;
  margin-bottom: 25px;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4dc8ff;
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn-secondary,
.btn-primary {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  color: #0a0f1c;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(77, 200, 255, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 15px;
    text-align: center;
    justify-content: space-between;
  }

  .header-center {
    order: 2;
    flex: 1;
    min-width: 200px;
  }

  .back-button {
    order: 1;
    flex-shrink: 0;
  }

  .language-dropdown {
    order: 3;
    flex-shrink: 0;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .language-dropdown-menu {
    right: 0;
    left: auto;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-center {
    order: 1;
  }

  .back-button {
    order: 2;
    align-self: flex-start;
  }

  .language-dropdown {
    order: 3;
    align-self: flex-end;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .language-dropdown-trigger {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 100px;
  }

  .language-dropdown-menu {
    min-width: 180px;
    right: 0;
    left: auto;
  }
}
